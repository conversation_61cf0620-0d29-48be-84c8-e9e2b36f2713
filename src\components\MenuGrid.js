import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Alert,
  Platform,
} from 'react-native';
import { Colors, Typography, Spacing } from '../constants';
import { MenuCard } from './';
import { MenuData } from '../constants/MenuData';

// Dynamic spacing calculation based on screen width and optimal utilization
const CARDS_PER_ROW = 4; // 4 columns layout
const ROWS = 2; // Define number of rows
const MIN_TOUCH_TARGET = 44; // Minimum touch target size for accessibility

// Dynamic spacing calculation for optimal space utilization
const calculateDynamicSpacing = (currentScreenWidth) => {
  // Base grid padding - responsive to screen size
  const GRID_PADDING = currentScreenWidth > 400 ? Spacing.padding.md : Spacing.padding.sm;

  // Dynamic card margin based on available space
  // Optimized margin for better space utilization while maintaining breathing room
  let CARD_MARGIN;
  if (currentScreenWidth > 500) {
    CARD_MARGIN = 2; // Larger screens can afford slightly more spacing
  } else if (currentScreenWidth > 350) {
    CARD_MARGIN = 1.5; // Medium screens get moderate spacing
  } else {
    CARD_MARGIN = 1; // Small screens need minimal spacing for optimal fit
  }

  // For web, limit maximum width for better desktop experience
  const MAX_CONTAINER_WIDTH = Platform.OS === 'web' ? 600 : currentScreenWidth;
  const EFFECTIVE_WIDTH = Math.min(currentScreenWidth, MAX_CONTAINER_WIDTH);

  // Calculate total spacing needed
  const TOTAL_HORIZONTAL_SPACING = (GRID_PADDING * 2) + (CARD_MARGIN * (CARDS_PER_ROW - 1));

  // Calculate optimal card width for maximum space utilization
  const CALCULATED_CARD_WIDTH = (EFFECTIVE_WIDTH - TOTAL_HORIZONTAL_SPACING) / CARDS_PER_ROW;

  // Ensure card width meets minimum touch target requirements
  const CARD_WIDTH = Math.max(CALCULATED_CARD_WIDTH, MIN_TOUCH_TARGET);

  return {
    GRID_PADDING,
    CARD_MARGIN,
    CARD_WIDTH,
    EFFECTIVE_WIDTH,
    TOTAL_HORIZONTAL_SPACING
  };
};

const MenuGrid = () => {
  // State for responsive layout
  const [screenData, setScreenData] = useState(Dimensions.get('window'));

  // Listen for orientation changes
  useEffect(() => {
    const onChange = (result) => {
      setScreenData(result.window);
    };

    const subscription = Dimensions.addEventListener('change', onChange);
    return () => subscription?.remove();
  }, []);

  // Calculate dynamic spacing based on current screen dimensions
  const {
    GRID_PADDING,
    CARD_MARGIN,
    CARD_WIDTH
  } = calculateDynamicSpacing(screenData.width);

  const handleMenuPress = (item) => {
    // For now, show an alert. In a real app, this would navigate to the respective screen
    Alert.alert(
      item.title,
      item.description,
      [
        {
          text: 'Batal',
          style: 'cancel',
        },
        {
          text: 'Buka Aplikasi',
          onPress: () => {
            console.log(`Navigate to ${item.route}`);
            // Navigation logic would go here
          },
        },
      ]
    );
  };

  const renderMenuItem = (item, dynamicStyles) => {
    return (
      <View key={item.id} style={dynamicStyles.cardContainer}>
        <MenuCard
          title={item.title}
          subtitle={item.subtitle}
          icon={item.icon}
          iconType={item.iconType}
          color={item.color}
          onPress={() => handleMenuPress(item)}
        />
      </View>
    );
  };

  const renderRow = (rowData, rowIndex, dynamicStyles) => {
    return (
      <View key={`row-${rowIndex}`} style={dynamicStyles.row}>
        {rowData.map((item) => renderMenuItem(item, dynamicStyles))}
      </View>
    );
  };

  const renderHeader = () => (
    <View style={staticStyles.headerContainer}>
      <Text style={staticStyles.sectionTitle}>Quick Actions</Text>
      <Text style={staticStyles.sectionSubtitle}>
        Akses cepat ke aplikasi yang sering digunakan
      </Text>
    </View>
  );

  // Split data into rows of 4 items each
  const createRows = () => {
    const rows = [];
    for (let i = 0; i < MenuData.length; i += CARDS_PER_ROW) {
      rows.push(MenuData.slice(i, i + CARDS_PER_ROW));
    }
    return rows;
  };

  const rows = createRows();

  // Dynamic styles based on current screen dimensions
  const dynamicStyles = StyleSheet.create({
    container: {
      paddingHorizontal: GRID_PADDING,
    },
    gridContainer: {
      paddingBottom: Spacing.sm,
      // Center the grid on web for better visual balance
      ...(Platform.OS === 'web' && {
        maxWidth: 600,
        alignSelf: 'center',
      }),
    },
    row: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      marginBottom: CARD_MARGIN * 2, // Dynamic vertical spacing
      paddingHorizontal: CARD_MARGIN / 2, // Dynamic horizontal padding
      alignItems: 'stretch',
    },
    cardContainer: {
      width: CARD_WIDTH,
      marginBottom: CARD_MARGIN,
      minHeight: Math.max(MIN_TOUCH_TARGET, 100),
      marginHorizontal: CARD_MARGIN / 2, // Dynamic horizontal margin
      flex: 0,
      maxWidth: CARD_WIDTH,
    },
  });

  return (
    <View style={dynamicStyles.container}>
      {renderHeader()}

      <View style={dynamicStyles.gridContainer}>
        {rows.map((rowData, rowIndex) => renderRow(rowData, rowIndex, dynamicStyles))}
      </View>
    </View>
  );
};

// Static styles that don't depend on dynamic spacing
const staticStyles = StyleSheet.create({
  headerContainer: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    ...Typography.h3,
    color: Colors.onSurface,
    marginBottom: Spacing.xs,
  },
  sectionSubtitle: {
    ...Typography.body2,
    color: Colors.onSurfaceVariant,
  },
});

export default MenuGrid;
