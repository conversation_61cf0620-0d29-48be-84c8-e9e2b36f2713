import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Alert,
  Platform,
} from 'react-native';
import { Colors, Typography, Spacing } from '../constants';
import { MenuCard } from './';
import { MenuData } from '../constants/MenuData';

// Dynamic responsive layout calculation
const MIN_TOUCH_TARGET = 44; // Minimum touch target size for accessibility

// Responsive breakpoints for optimal mobile experience
const BREAKPOINTS = {
  SMALL_MOBILE: 320,   // Small phones
  MOBILE: 414,         // Standard phones
  TABLET: 768,         // Tablets
  DESKTOP: 1024        // Desktop
};

// Dynamic layout calculation with mobile-first approach
const calculateResponsiveLayout = (currentScreenWidth) => {
  let CARDS_PER_ROW, ROWS, GRID_PADDING, CARD_MARGIN;

  // Mobile-first responsive design
  if (currentScreenWidth <= BREAKPOINTS.SMALL_MOBILE) {
    // Very small phones: 2 columns x 4 rows for better visibility
    CARDS_PER_ROW = 2;
    ROWS = 4;
    GRID_PADDING = Spacing.padding.sm;
    CARD_MARGIN = 4;
  } else if (currentScreenWidth <= BREAKPOINTS.MOBILE) {
    // Standard phones: 2 columns x 4 rows for optimal mobile experience
    CARDS_PER_ROW = 2;
    ROWS = 4;
    GRID_PADDING = Spacing.padding.md;
    CARD_MARGIN = 6;
  } else if (currentScreenWidth <= BREAKPOINTS.TABLET) {
    // Tablets: 4 columns x 2 rows
    CARDS_PER_ROW = 4;
    ROWS = 2;
    GRID_PADDING = Spacing.padding.md;
    CARD_MARGIN = 8;
  } else {
    // Desktop: 4 columns x 2 rows with more spacing
    CARDS_PER_ROW = 4;
    ROWS = 2;
    GRID_PADDING = Spacing.padding.lg;
    CARD_MARGIN = 12;
  }

  // For web, limit maximum width for better desktop experience
  const MAX_CONTAINER_WIDTH = Platform.OS === 'web' ? 600 : currentScreenWidth;
  const EFFECTIVE_WIDTH = Math.min(currentScreenWidth, MAX_CONTAINER_WIDTH);

  // Calculate total spacing needed
  const TOTAL_HORIZONTAL_SPACING = (GRID_PADDING * 2) + (CARD_MARGIN * (CARDS_PER_ROW - 1));

  // Calculate optimal card width for maximum space utilization
  const CALCULATED_CARD_WIDTH = (EFFECTIVE_WIDTH - TOTAL_HORIZONTAL_SPACING) / CARDS_PER_ROW;

  // Ensure card width meets minimum touch target requirements and doesn't exceed safe limits
  const MAX_CARD_WIDTH = CARDS_PER_ROW === 2 ? 180 : 120; // Maximum safe width per layout
  const CARD_WIDTH = Math.max(
    Math.min(CALCULATED_CARD_WIDTH, MAX_CARD_WIDTH),
    MIN_TOUCH_TARGET
  );

  return {
    CARDS_PER_ROW,
    ROWS,
    GRID_PADDING,
    CARD_MARGIN,
    CARD_WIDTH,
    EFFECTIVE_WIDTH,
    TOTAL_HORIZONTAL_SPACING
  };
};

const MenuGrid = () => {
  // State for responsive layout
  const [screenData, setScreenData] = useState(Dimensions.get('window'));

  // Listen for orientation changes
  useEffect(() => {
    const onChange = (result) => {
      setScreenData(result.window);
    };

    const subscription = Dimensions.addEventListener('change', onChange);
    return () => subscription?.remove();
  }, []);

  // Calculate responsive layout based on current screen dimensions
  const {
    CARDS_PER_ROW,
    ROWS,
    GRID_PADDING,
    CARD_MARGIN,
    CARD_WIDTH
  } = calculateResponsiveLayout(screenData.width);



  const handleMenuPress = (item) => {
    // For now, show an alert. In a real app, this would navigate to the respective screen
    Alert.alert(
      item.title,
      item.description,
      [
        {
          text: 'Batal',
          style: 'cancel',
        },
        {
          text: 'Buka Aplikasi',
          onPress: () => {
            console.log(`Navigate to ${item.route}`);
            // Navigation logic would go here
          },
        },
      ]
    );
  };

  const renderMenuItem = (item, dynamicStyles) => {
    return (
      <View key={item.id} style={dynamicStyles.cardContainer}>
        <MenuCard
          title={item.title}
          subtitle={item.subtitle}
          icon={item.icon}
          iconType={item.iconType}
          color={item.color}
          onPress={() => handleMenuPress(item)}
        />
      </View>
    );
  };

  const renderRow = (rowData, rowIndex, dynamicStyles) => {
    return (
      <View key={`row-${rowIndex}`} style={dynamicStyles.row}>
        {rowData.map((item) => renderMenuItem(item, dynamicStyles))}
      </View>
    );
  };

  const renderHeader = () => (
    <View style={staticStyles.headerContainer}>
      <Text style={staticStyles.sectionTitle}>Quick Actions</Text>
      <Text style={staticStyles.sectionSubtitle}>
        Akses cepat ke aplikasi yang sering digunakan
      </Text>
    </View>
  );

  // Split data into rows of 4 items each
  const createRows = () => {
    const rows = [];
    for (let i = 0; i < MenuData.length; i += CARDS_PER_ROW) {
      rows.push(MenuData.slice(i, i + CARDS_PER_ROW));
    }
    return rows;
  };

  const rows = createRows();

  // Dynamic styles based on current screen dimensions and responsive layout
  const dynamicStyles = StyleSheet.create({
    container: {
      paddingHorizontal: GRID_PADDING,
      // Prevent horizontal overflow
      overflow: 'hidden',
    },
    gridContainer: {
      paddingBottom: Spacing.sm,
      // Prevent horizontal overflow
      overflow: 'hidden',
      // Center the grid on web for better visual balance
      ...(Platform.OS === 'web' && {
        maxWidth: 600,
        alignSelf: 'center',
      }),
    },
    row: {
      flexDirection: 'row',
      justifyContent: CARDS_PER_ROW === 2 ? 'space-around' : 'space-between', // Better spacing for 2-column layout
      marginBottom: CARD_MARGIN, // Responsive vertical spacing
      paddingHorizontal: CARDS_PER_ROW === 2 ? CARD_MARGIN : CARD_MARGIN / 2, // Responsive horizontal padding
      alignItems: 'stretch',
      // Ensure row doesn't overflow
      flexWrap: 'nowrap',
      overflow: 'hidden',
    },
    cardContainer: {
      width: CARD_WIDTH,
      marginBottom: CARD_MARGIN,
      // Responsive minimum height based on layout
      minHeight: Math.max(MIN_TOUCH_TARGET, CARDS_PER_ROW === 2 ? 120 : 100),
      marginHorizontal: CARDS_PER_ROW === 2 ? CARD_MARGIN / 2 : CARD_MARGIN / 4, // Responsive horizontal margin
      flex: 0,
      maxWidth: CARD_WIDTH,
      // Prevent card overflow
      overflow: 'hidden',
    },
  });

  return (
    <View style={dynamicStyles.container}>
      {renderHeader()}

      <View style={dynamicStyles.gridContainer}>
        {rows.map((rowData, rowIndex) => renderRow(rowData, rowIndex, dynamicStyles))}
      </View>
    </View>
  );
};

// Static styles that don't depend on dynamic spacing
const staticStyles = StyleSheet.create({
  headerContainer: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    ...Typography.h3,
    color: Colors.onSurface,
    marginBottom: Spacing.xs,
  },
  sectionSubtitle: {
    ...Typography.body2,
    color: Colors.onSurfaceVariant,
  },
});

export default MenuGrid;
