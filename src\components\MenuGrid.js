import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  Dimensions,
  Alert,
  Platform,
} from 'react-native';
import { Colors, Typography, Spacing } from '../constants';
import { MenuCard } from './';
import { MenuData } from '../constants/MenuData';

const { width: screenWidth } = Dimensions.get('window');
const GRID_PADDING = Spacing.padding.md;
const CARD_MARGIN = Spacing.margin.xs; // Reduced margin for tighter spacing in 4-column layout
const CARDS_PER_ROW = 4; // Changed from 2 to 4 for 4 columns layout
const ROWS = 2; // Define number of rows

// Calculate card width with proper spacing for 4 columns
// For web, we might want to limit the maximum width
const MAX_CONTAINER_WIDTH = Platform.OS === 'web' ? 600 : screenWidth;
const EFFECTIVE_WIDTH = Math.min(screenWidth, MAX_CONTAINER_WIDTH);
const TOTAL_HORIZONTAL_SPACING = (GRID_PADDING * 2) + (CARD_MARGIN * (CARDS_PER_ROW - 1));
const CARD_WIDTH = (EFFECTIVE_WIDTH - TOTAL_HORIZONTAL_SPACING) / CARDS_PER_ROW;
const MIN_TOUCH_TARGET = 44; // Minimum touch target size for accessibility

const MenuGrid = () => {
  const handleMenuPress = (item) => {
    // For now, show an alert. In a real app, this would navigate to the respective screen
    Alert.alert(
      item.title,
      item.description,
      [
        {
          text: 'Batal',
          style: 'cancel',
        },
        {
          text: 'Buka Aplikasi',
          onPress: () => {
            console.log(`Navigate to ${item.route}`);
            // Navigation logic would go here
          },
        },
      ]
    );
  };

  const renderMenuItem = (item, index) => {
    return (
      <View key={item.id} style={styles.cardContainer}>
        <MenuCard
          title={item.title}
          subtitle={item.subtitle}
          icon={item.icon}
          iconType={item.iconType}
          color={item.color}
          onPress={() => handleMenuPress(item)}
        />
      </View>
    );
  };

  const renderRow = (rowData, rowIndex) => {
    return (
      <View key={`row-${rowIndex}`} style={styles.row}>
        {rowData.map((item, index) => renderMenuItem(item, index))}
      </View>
    );
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <Text style={styles.sectionTitle}>Quick Actions</Text>
      <Text style={styles.sectionSubtitle}>
        Akses cepat ke aplikasi yang sering digunakan
      </Text>
    </View>
  );

  // Split data into rows of 4 items each
  const createRows = () => {
    const rows = [];
    for (let i = 0; i < MenuData.length; i += CARDS_PER_ROW) {
      rows.push(MenuData.slice(i, i + CARDS_PER_ROW));
    }
    return rows;
  };

  const rows = createRows();

  return (
    <View style={styles.container}>
      {renderHeader()}

      <View style={styles.gridContainer}>
        {rows.map((rowData, rowIndex) => renderRow(rowData, rowIndex))}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    paddingHorizontal: GRID_PADDING,
  },
  headerContainer: {
    marginBottom: Spacing.lg,
  },
  sectionTitle: {
    ...Typography.h3,
    color: Colors.onSurface,
    marginBottom: Spacing.xs,
  },
  sectionSubtitle: {
    ...Typography.body2,
    color: Colors.onSurfaceVariant,
  },
  gridContainer: {
    paddingBottom: Spacing.sm,
    // Center the grid on web for better visual balance
    ...(Platform.OS === 'web' && {
      maxWidth: MAX_CONTAINER_WIDTH,
      alignSelf: 'center',
    }),
  },
  row: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: Spacing.sm,
    // Ensure proper spacing between rows
    paddingHorizontal: 0,
    alignItems: 'stretch', // Ensure all cards in a row have the same height
  },
  cardContainer: {
    width: CARD_WIDTH,
    marginBottom: CARD_MARGIN,
    // Ensure minimum touch target size
    minHeight: Math.max(MIN_TOUCH_TARGET, 100),
    // Remove horizontal margin as we're using space-between
    marginHorizontal: 0,
    flex: 0, // Prevent flex growth
    // Add subtle border for better definition on web
    ...(Platform.OS === 'web' && {
      borderWidth: 0.5,
      borderColor: Colors.outline + '20',
      borderRadius: Spacing.borderRadius.lg,
    }),
  },
});

export default MenuGrid;
