import React, { useRef, useState, useEffect, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  FlatList,
  TouchableOpacity,
  Image,
  Dimensions,
  Alert,
  ActivityIndicator,
  Platform,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors, Typography, Spacing } from '../constants';
import { activityService } from '../services';

const { width: screenWidth } = Dimensions.get('window');
const ACTIVITY_CARD_WIDTH = screenWidth * 0.7;
const ACTIVITY_CARD_HEIGHT = 220;

const ActivityGallery = () => {
  const flatListRef = useRef(null);
  const autoScrollTimer = useRef(null);
  const userInteractionTimer = useRef(null);
  const lastScrollTime = useRef(0);
  const isMounted = useRef(true);

  // State management
  const [activities, setActivities] = useState([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isAutoScrollEnabled, setIsAutoScrollEnabled] = useState(true);
  const [isUserInteracting, setIsUserInteracting] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [isLayoutReady, setIsLayoutReady] = useState(false);
  const [hasError, setHasError] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);

  // Configuration constants
  const AUTO_SCROLL_INTERVAL = 4500; // 4.5 seconds
  const USER_INTERACTION_PAUSE = 3000; // 3 seconds
  const SWIPE_THRESHOLD = 50; // 50px minimum swipe distance
  const INFINITE_LOOP = true; // Enable infinite loop

  // Fetch featured activities from API
  const fetchActivities = useCallback(async (forceRefresh = false) => {
    try {
      setIsLoading(true);
      setError(null);
      setHasError(false);

      const response = await activityService.getFeaturedActivities({
        limit: 8,
        forceRefresh,
      });

      if (isMounted.current) {
        setActivities(response.activities || []);
        setIsLoading(false);
      }
    } catch (err) {
      console.error('Error fetching activities:', err);
      if (isMounted.current) {
        setError(err.message || 'Failed to load activities');
        setHasError(true);
        setIsLoading(false);
      }
    }
  }, []);

  // Safe state setter to prevent memory leaks
  const safeSetState = useCallback((setter, value) => {
    if (isMounted.current) {
      setter(value);
    }
  }, []);

  // Initialize component
  useEffect(() => {
    isMounted.current = true;
    fetchActivities();

    return () => {
      isMounted.current = false;
      if (autoScrollTimer.current) {
        clearInterval(autoScrollTimer.current);
      }
      if (userInteractionTimer.current) {
        clearTimeout(userInteractionTimer.current);
      }
    };
  }, [fetchActivities]);

  // Handle activity press
  const handleActivityPress = async (activity) => {
    try {
      // Record activity view
      await activityService.recordActivityView(activity.id);

      Alert.alert(
        activity.title,
        `Tanggal: ${new Date(activity.date).toLocaleDateString('id-ID')}\nKategori: ${activity.category}\nDilihat: ${activity.views} kali`,
        [
          {
            text: 'Batal',
            style: 'cancel',
          },
          {
            text: 'Lihat Detail',
            onPress: () => {
              console.log('View activity details:', activity.title);
              // Activity detail navigation would go here
            },
          },
        ]
      );
    } catch (error) {
      console.error('Error handling activity press:', error);
      Alert.alert('Error', 'Terjadi kesalahan saat memuat aktivitas');
    }
  };

  const handleViewAll = () => {
    console.log('View all activities');
    // Navigate to full activity gallery
  };

  const formatViews = (views) => {
    if (views >= 1000) {
      return `${(views / 1000).toFixed(1)}k`;
    }
    return views.toString();
  };

  // Auto-scroll functionality
  const startAutoScroll = useCallback(() => {
    if (!isAutoScrollEnabled || activities.length <= 1 || isUserInteracting || isDragging) {
      return;
    }

    if (autoScrollTimer.current) {
      clearInterval(autoScrollTimer.current);
    }

    autoScrollTimer.current = setInterval(() => {
      if (!isMounted.current || isUserInteracting || isDragging || !isLayoutReady) {
        return;
      }

      const nextIndex = INFINITE_LOOP 
        ? (currentIndex + 1) % activities.length
        : Math.min(currentIndex + 1, activities.length - 1);

      if (nextIndex !== currentIndex) {
        scrollToIndex(nextIndex);
      }
    }, AUTO_SCROLL_INTERVAL);
  }, [isAutoScrollEnabled, activities.length, isUserInteracting, isDragging, currentIndex, isLayoutReady]);

  const stopAutoScroll = useCallback(() => {
    if (autoScrollTimer.current) {
      clearInterval(autoScrollTimer.current);
      autoScrollTimer.current = null;
    }
  }, []);

  // Scroll to specific index
  const scrollToIndex = useCallback((index) => {
    if (!flatListRef.current || !isLayoutReady || activities.length === 0) {
      return;
    }

    const validIndex = Math.max(0, Math.min(index, activities.length - 1));
    
    try {
      flatListRef.current.scrollToIndex({
        index: validIndex,
        animated: true,
      });
      safeSetState(setCurrentIndex, validIndex);
      lastScrollTime.current = Date.now();
    } catch (error) {
      console.warn('ScrollToIndex error:', error);
      // Fallback to scrollToOffset
      const offset = validIndex * (ACTIVITY_CARD_WIDTH + Spacing.md);
      flatListRef.current.scrollToOffset({
        offset,
        animated: true,
      });
    }
  }, [isLayoutReady, activities.length, safeSetState]);

  // Handle user interaction pause
  const handleUserInteraction = useCallback(() => {
    safeSetState(setIsUserInteracting, true);
    stopAutoScroll();

    if (userInteractionTimer.current) {
      clearTimeout(userInteractionTimer.current);
    }

    userInteractionTimer.current = setTimeout(() => {
      if (isMounted.current) {
        safeSetState(setIsUserInteracting, false);
      }
    }, USER_INTERACTION_PAUSE);
  }, [safeSetState, stopAutoScroll]);

  // Handle scroll events
  const handleScroll = useCallback((event) => {
    if (!isLayoutReady || isDragging) return;

    const { contentOffset } = event.nativeEvent;
    const index = Math.round(contentOffset.x / (ACTIVITY_CARD_WIDTH + Spacing.md));
    const validIndex = Math.max(0, Math.min(index, activities.length - 1));

    if (validIndex !== currentIndex) {
      safeSetState(setCurrentIndex, validIndex);
    }
  }, [isLayoutReady, isDragging, activities.length, currentIndex, safeSetState]);

  // Handle scroll begin drag
  const handleScrollBeginDrag = useCallback(() => {
    handleUserInteraction();
    safeSetState(setIsDragging, true);
  }, [handleUserInteraction, safeSetState]);

  // Handle scroll end drag
  const handleScrollEndDrag = useCallback(() => {
    if (userInteractionTimer.current) {
      clearTimeout(userInteractionTimer.current);
    }

    userInteractionTimer.current = setTimeout(() => {
      if (isMounted.current) {
        safeSetState(setIsDragging, false);
      }
    }, 100);

    safeSetState(setIsDragging, false);
  }, [currentIndex, safeSetState]);

  const renderActivityItem = ({ item }) => {
    return (
      <TouchableOpacity
        style={styles.activityCard}
        onPress={() => handleActivityPress(item)}
        activeOpacity={0.9}
      >
        <View style={styles.imageContainer}>
          <Image source={{ uri: item.image }} style={styles.activityImage} resizeMode="cover" />
          <View style={styles.categoryBadge}>
            <Text style={styles.categoryText}>{item.category}</Text>
          </View>
          <View style={styles.dateBadge}>
            <Text style={styles.dateText}>
              {new Date(item.date).toLocaleDateString('id-ID', {
                day: 'numeric',
                month: 'short'
              })}
            </Text>
          </View>
        </View>

        <View style={styles.activityInfo}>
          <Text style={styles.activityTitle} numberOfLines={2}>
            {item.title}
          </Text>
          <Text style={styles.activityDescription} numberOfLines={2}>
            {item.description}
          </Text>
          <View style={styles.activityMeta}>
            <View style={styles.metaInfo}>
              <MaterialIcons
                name="visibility"
                size={14}
                color={Colors.onSurfaceVariant}
              />
              <Text style={styles.viewsText}>{formatViews(item.views)}</Text>
            </View>
            <View style={styles.metaInfo}>
              <MaterialIcons
                name="favorite"
                size={14}
                color={Colors.error}
              />
              <Text style={styles.likesText}>{item.likes || 0}</Text>
            </View>
          </View>
        </View>
      </TouchableOpacity>
    );
  };

  const renderHeader = () => (
    <View style={styles.headerContainer}>
      <View style={styles.titleContainer}>
        <Text style={styles.sectionTitle}>Galeri Aktivitas</Text>
        <Text style={styles.sectionSubtitle}>
          Kegiatan dan acara terbaru perusahaan
        </Text>
      </View>
      <TouchableOpacity onPress={handleViewAll} style={styles.viewAllButton}>
        <Text style={styles.viewAllText}>Lihat Semua</Text>
        <MaterialIcons
          name="arrow-forward"
          size={16}
          color={Colors.primary}
        />
      </TouchableOpacity>
    </View>
  );

  const renderNavigationControls = () => {
    const isPrevDisabled = !INFINITE_LOOP && currentIndex === 0;
    const isNextDisabled = !INFINITE_LOOP && currentIndex === activities.length - 1;

    return (
      <View style={styles.navigationContainer}>
        {/* Pagination Dots - Center */}
        <View style={styles.paginationContainer}>
          {activities.map((_, index) => (
            <TouchableOpacity
              key={index}
              style={[
                styles.paginationDot,
                index === currentIndex && styles.paginationDotActive
              ]}
              onPress={() => {
                handleUserInteraction();
                scrollToIndex(index);
              }}
            />
          ))}
        </View>

        {/* Navigation Arrows - Right */}
        <View style={styles.arrowContainer}>
          <TouchableOpacity
            style={[styles.navButton, isPrevDisabled && styles.navButtonDisabled]}
            onPress={() => {
              if (!isPrevDisabled) {
                handleUserInteraction();
                const prevIndex = INFINITE_LOOP
                  ? (currentIndex - 1 + activities.length) % activities.length
                  : Math.max(0, currentIndex - 1);
                scrollToIndex(prevIndex);
              }
            }}
            disabled={isPrevDisabled}
          >
            <MaterialIcons
              name="chevron-left"
              size={24}
              color={isPrevDisabled ? Colors.onSurfaceVariant : Colors.onSurface}
            />
          </TouchableOpacity>

          <TouchableOpacity
            style={[styles.navButton, isNextDisabled && styles.navButtonDisabled]}
            onPress={() => {
              if (!isNextDisabled) {
                handleUserInteraction();
                const nextIndex = INFINITE_LOOP
                  ? (currentIndex + 1) % activities.length
                  : Math.min(activities.length - 1, currentIndex + 1);
                scrollToIndex(nextIndex);
              }
            }}
            disabled={isNextDisabled}
          >
            <MaterialIcons
              name="chevron-right"
              size={24}
              color={isNextDisabled ? Colors.onSurfaceVariant : Colors.onSurface}
            />
          </TouchableOpacity>
        </View>
      </View>
    );
  };

  // Auto-scroll effect
  useEffect(() => {
    if (isLayoutReady && activities.length > 1) {
      startAutoScroll();
    }

    return () => {
      stopAutoScroll();
    };
  }, [isLayoutReady, activities.length, startAutoScroll, stopAutoScroll]);

  // Handle layout ready
  const handleLayoutReady = useCallback(() => {
    if (!isLayoutReady) {
      safeSetState(setIsLayoutReady, true);
    }
  }, [isLayoutReady, safeSetState]);

  // Error state
  if (hasError) {
    return (
      <View style={styles.container}>
        {renderHeader()}
        <View style={styles.errorContainer}>
          <MaterialIcons name="error-outline" size={48} color={Colors.error} />
          <Text style={styles.errorText}>Gagal memuat galeri aktivitas</Text>
          <Text style={styles.errorSubtext}>{error}</Text>
          <TouchableOpacity
            style={styles.retryButton}
            onPress={() => fetchActivities(true)}
          >
            <Text style={styles.retryButtonText}>Coba Lagi</Text>
          </TouchableOpacity>
        </View>
      </View>
    );
  }

  // Loading state
  if (isLoading) {
    return (
      <View style={styles.container}>
        {renderHeader()}
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <Text style={styles.loadingText}>Memuat galeri aktivitas...</Text>
        </View>
      </View>
    );
  }

  // Empty state
  if (activities.length === 0) {
    return (
      <View style={styles.container}>
        {renderHeader()}
        <View style={styles.emptyContainer}>
          <MaterialIcons name="photo-library" size={48} color={Colors.onSurfaceVariant} />
          <Text style={styles.emptyText}>Belum ada aktivitas</Text>
          <Text style={styles.emptySubtext}>Galeri aktivitas akan muncul di sini</Text>
        </View>
      </View>
    );
  }

  // Use platform-specific container
  const Container = Platform.OS === 'web' ? View : View; // For now, just use View for all platforms

  return (
    <Container style={styles.container}>
      {renderHeader()}

      <View style={styles.galleryContainer}>
        <FlatList
          ref={flatListRef}
          data={activities}
          renderItem={renderActivityItem}
          keyExtractor={(item) => item.id.toString()}
          horizontal
          showsHorizontalScrollIndicator={false}
          contentContainerStyle={styles.listContainer}
          snapToInterval={ACTIVITY_CARD_WIDTH + Spacing.md}
          decelerationRate="fast"
          onScroll={handleScroll}
          onScrollBeginDrag={handleScrollBeginDrag}
          onScrollEndDrag={handleScrollEndDrag}
          onLayout={handleLayoutReady}
          scrollEventThrottle={16}
          removeClippedSubviews={true}
          initialNumToRender={3}
          maxToRenderPerBatch={5}
          windowSize={10}
          getItemLayout={(data, index) => ({
            length: ACTIVITY_CARD_WIDTH + Spacing.md,
            offset: (ACTIVITY_CARD_WIDTH + Spacing.md) * index,
            index,
          })}
        />
      </View>

      {renderNavigationControls()}
    </Container>
  );
};

const styles = StyleSheet.create({
  container: {
    marginBottom: Spacing.lg,
  },
  headerContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    marginBottom: Spacing.md,
    paddingHorizontal: Spacing.md,
  },
  titleContainer: {
    flex: 1,
  },
  sectionTitle: {
    ...Typography.headlineSmall,
    color: Colors.onSurface,
    marginBottom: Spacing.xs,
  },
  sectionSubtitle: {
    ...Typography.bodyMedium,
    color: Colors.onSurfaceVariant,
  },
  viewAllButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: Spacing.xs,
    paddingHorizontal: Spacing.sm,
  },
  viewAllText: {
    ...Typography.labelLarge,
    color: Colors.primary,
    marginRight: Spacing.xs,
  },
  galleryContainer: {
    marginBottom: Spacing.md,
  },
  listContainer: {
    paddingHorizontal: Spacing.md,
  },
  activityCard: {
    width: ACTIVITY_CARD_WIDTH,
    height: ACTIVITY_CARD_HEIGHT,
    marginRight: Spacing.md,
    backgroundColor: Colors.surface,
    borderRadius: 16,
    elevation: 2,
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    overflow: 'hidden',
  },
  imageContainer: {
    position: 'relative',
    height: 140,
    backgroundColor: Colors.surfaceVariant,
  },
  activityImage: {
    width: '100%',
    height: '100%',
  },
  categoryBadge: {
    position: 'absolute',
    top: Spacing.sm,
    left: Spacing.sm,
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: 12,
  },
  categoryText: {
    ...Typography.labelSmall,
    color: Colors.onPrimary,
    fontWeight: '600',
  },
  dateBadge: {
    position: 'absolute',
    top: Spacing.sm,
    right: Spacing.sm,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: Spacing.sm,
    paddingVertical: Spacing.xs,
    borderRadius: 12,
  },
  dateText: {
    ...Typography.labelSmall,
    color: Colors.onPrimary,
    fontWeight: '500',
  },
  activityInfo: {
    flex: 1,
    padding: Spacing.md,
    justifyContent: 'space-between',
  },
  activityTitle: {
    ...Typography.titleSmall,
    color: Colors.onSurface,
    marginBottom: Spacing.xs,
    lineHeight: 20,
  },
  activityDescription: {
    ...Typography.bodySmall,
    color: Colors.onSurfaceVariant,
    marginBottom: Spacing.sm,
    lineHeight: 16,
  },
  activityMeta: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  metaInfo: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  viewsText: {
    ...Typography.labelSmall,
    color: Colors.onSurfaceVariant,
    marginLeft: Spacing.xs,
  },
  likesText: {
    ...Typography.labelSmall,
    color: Colors.onSurfaceVariant,
    marginLeft: Spacing.xs,
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: Spacing.md,
    marginTop: Spacing.sm,
  },
  paginationContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
    justifyContent: 'center',
  },
  paginationDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: Colors.outline,
    marginHorizontal: 4,
  },
  paginationDotActive: {
    backgroundColor: Colors.primary,
    width: 24,
  },
  arrowContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  navButton: {
    width: 40,
    height: 40,
    borderRadius: 20,
    backgroundColor: Colors.surfaceVariant,
    justifyContent: 'center',
    alignItems: 'center',
    marginLeft: Spacing.sm,
  },
  navButtonDisabled: {
    backgroundColor: Colors.surfaceVariant,
    opacity: 0.5,
  },
  // Loading state
  loadingContainer: {
    height: ACTIVITY_CARD_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: 16,
    marginHorizontal: Spacing.md,
  },
  loadingText: {
    ...Typography.bodyMedium,
    color: Colors.onSurfaceVariant,
    marginTop: Spacing.md,
  },
  // Error state
  errorContainer: {
    height: ACTIVITY_CARD_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: 16,
    marginHorizontal: Spacing.md,
    padding: Spacing.lg,
  },
  errorText: {
    ...Typography.titleMedium,
    color: Colors.error,
    marginTop: Spacing.md,
    textAlign: 'center',
  },
  errorSubtext: {
    ...Typography.bodySmall,
    color: Colors.onSurfaceVariant,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingHorizontal: Spacing.lg,
    paddingVertical: Spacing.sm,
    borderRadius: 20,
    marginTop: Spacing.md,
  },
  retryButtonText: {
    ...Typography.labelLarge,
    color: Colors.onPrimary,
  },
  // Empty state
  emptyContainer: {
    height: ACTIVITY_CARD_HEIGHT,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.surface,
    borderRadius: 16,
    marginHorizontal: Spacing.md,
  },
  emptyText: {
    ...Typography.titleMedium,
    color: Colors.onSurfaceVariant,
    marginTop: Spacing.md,
  },
  emptySubtext: {
    ...Typography.bodySmall,
    color: Colors.onSurfaceVariant,
    marginTop: Spacing.xs,
    textAlign: 'center',
  },
});

export default ActivityGallery;
