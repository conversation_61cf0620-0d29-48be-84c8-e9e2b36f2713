import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Platform,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors, Typography, Spacing, Animations } from '../constants';

const MenuCard = ({
  title,
  subtitle,
  icon,
  iconType = 'MaterialIcons',
  color = Colors.primary,
  onPress,
  style,
  disabled = false,
}) => {
  const scaleValue = React.useRef(new Animated.Value(1)).current;
  const opacityValue = React.useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.parallel([
      Animated.spring(scaleValue, {
        toValue: Animations.scale.pressed,
        ...Animations.spring.default,
        useNativeDriver: true,
      }),
      Animated.timing(opacityValue, {
        toValue: 0.8,
        duration: Animations.duration.fast,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    Animated.parallel([
      Animated.spring(scaleValue, {
        toValue: Animations.scale.normal,
        ...Animations.spring.default,
        useNativeDriver: true,
      }),
      Animated.timing(opacityValue, {
        toValue: 1,
        duration: Animations.duration.fast,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const IconComponent = MaterialIcons;

  return (
    <Animated.View
      style={[
        {
          transform: [{ scale: scaleValue }],
          opacity: opacityValue,
        },
        style,
      ]}
    >
      <TouchableOpacity
        style={[styles.container, disabled && styles.disabled]}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled}
        activeOpacity={0.9}
      >
        <View style={[styles.iconContainer, { backgroundColor: `${color}15` }]}>
          <IconComponent
            name={icon}
            size={Spacing.iconSize.md} // Changed from lg to md for better fit in 4-column layout
            color={color}
          />
        </View>
        
        <View style={styles.textContainer}>
          <Text style={styles.title} numberOfLines={2}>
            {title}
          </Text>
          <Text style={styles.subtitle} numberOfLines={1}>
            {subtitle}
          </Text>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.surfaceVariant, // Changed from Colors.surface for better harmony
    borderRadius: Spacing.borderRadius.md, // Slightly reduced border radius for compact look
    padding: Spacing.padding.xs, // Further reduced padding for mobile optimization
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 90, // Optimized height for mobile screens
    shadowColor: Colors.cardShadow,
    shadowOffset: {
      width: 0,
      height: 1, // Reduced shadow for subtler effect
    },
    shadowOpacity: 0.08, // Reduced shadow opacity for cleaner look
    shadowRadius: 3, // Reduced shadow radius
    elevation: 2, // Reduced elevation for Android
    // Ensure minimum touch target
    minWidth: 44,
    // Add subtle border for better definition
    borderWidth: 0.5,
    borderColor: Colors.outline + '30', // Very subtle border
    // Optimize for mobile touch
    overflow: 'hidden', // Prevent content overflow
    // Add hover effect for web
    ...(Platform.OS === 'web' && {
      cursor: 'pointer',
      transition: 'all 0.2s ease-in-out',
      ':hover': {
        backgroundColor: Colors.surface, // Lighter on hover
        transform: 'translateY(-1px)', // Reduced hover lift
        shadowOpacity: 0.12,
        shadowRadius: 4,
      },
    }),
  },
  iconContainer: {
    width: 36, // Further reduced for mobile optimization
    height: 36,
    borderRadius: Spacing.borderRadius.md, // Consistent with card border radius
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.xs / 2, // Further reduced margin for mobile
    // Add subtle shadow for icon container depth
    shadowColor: Colors.shadow,
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.05,
    shadowRadius: 2,
    elevation: 1,
  },
  textContainer: {
    alignItems: 'center',
    flex: 1,
    paddingHorizontal: Spacing.xs, // Add horizontal padding for text
  },
  title: {
    ...Typography.caption, // Changed from subtitle2 to caption for smaller text
    fontWeight: '600', // Add font weight for better readability
    color: Colors.onSurface,
    textAlign: 'center',
    marginBottom: 2, // Slightly increased for better mobile readability
    lineHeight: 14, // Optimized line height for mobile readability
    fontSize: 12, // Optimized for mobile screens
    paddingHorizontal: 2, // Add horizontal padding to prevent text cutoff
  },
  subtitle: {
    ...Typography.caption,
    fontSize: 10, // Optimized font size for mobile readability
    color: Colors.onSurfaceVariant,
    textAlign: 'center',
    lineHeight: 12, // Better line height for mobile
    opacity: 0.8, // Slightly reduced opacity for visual hierarchy
    paddingHorizontal: 2, // Add horizontal padding to prevent text cutoff
  },
  disabled: {
    opacity: Animations.opacity.disabled,
  },
});

export default MenuCard;
