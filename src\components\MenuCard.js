import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Animated,
  Platform,
} from 'react-native';
import { MaterialIcons } from '@expo/vector-icons';
import { Colors, Typography, Spacing, Animations } from '../constants';

const MenuCard = ({
  title,
  subtitle,
  icon,
  iconType = 'MaterialIcons',
  color = Colors.primary,
  onPress,
  style,
  disabled = false,
}) => {
  const scaleValue = React.useRef(new Animated.Value(1)).current;
  const opacityValue = React.useRef(new Animated.Value(1)).current;

  const handlePressIn = () => {
    Animated.parallel([
      Animated.spring(scaleValue, {
        toValue: Animations.scale.pressed,
        ...Animations.spring.default,
        useNativeDriver: true,
      }),
      Animated.timing(opacityValue, {
        toValue: 0.8,
        duration: Animations.duration.fast,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const handlePressOut = () => {
    Animated.parallel([
      Animated.spring(scaleValue, {
        toValue: Animations.scale.normal,
        ...Animations.spring.default,
        useNativeDriver: true,
      }),
      Animated.timing(opacityValue, {
        toValue: 1,
        duration: Animations.duration.fast,
        useNativeDriver: true,
      }),
    ]).start();
  };

  const IconComponent = MaterialIcons;

  return (
    <Animated.View
      style={[
        {
          transform: [{ scale: scaleValue }],
          opacity: opacityValue,
        },
        style,
      ]}
    >
      <TouchableOpacity
        style={[styles.container, disabled && styles.disabled]}
        onPress={onPress}
        onPressIn={handlePressIn}
        onPressOut={handlePressOut}
        disabled={disabled}
        activeOpacity={0.9}
      >
        <View style={[styles.iconContainer, { backgroundColor: `${color}15` }]}>
          <IconComponent
            name={icon}
            size={Spacing.iconSize.md} // Changed from lg to md for better fit in 4-column layout
            color={color}
          />
        </View>
        
        <View style={styles.textContainer}>
          <Text style={styles.title} numberOfLines={2}>
            {title}
          </Text>
          <Text style={styles.subtitle} numberOfLines={1}>
            {subtitle}
          </Text>
        </View>
      </TouchableOpacity>
    </Animated.View>
  );
};

const styles = StyleSheet.create({
  container: {
    backgroundColor: Colors.surface,
    borderRadius: Spacing.borderRadius.lg,
    padding: Spacing.padding.sm, // Reduced padding for 4-column layout
    alignItems: 'center',
    justifyContent: 'center',
    minHeight: 100, // Reduced height for better fit in 4-column layout
    shadowColor: Colors.cardShadow,
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
    // Ensure minimum touch target
    minWidth: 44,
    // Add hover effect for web
    ...(Platform.OS === 'web' && {
      cursor: 'pointer',
      transition: 'all 0.2s ease-in-out',
      ':hover': {
        transform: 'translateY(-2px)',
        shadowOpacity: 0.15,
        shadowRadius: 6,
      },
    }),
  },
  iconContainer: {
    width: 40, // Reduced from 56 to fit better in smaller cards
    height: 40,
    borderRadius: Spacing.borderRadius.lg,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: Spacing.xs, // Reduced margin
  },
  textContainer: {
    alignItems: 'center',
    flex: 1,
    paddingHorizontal: Spacing.xs, // Add horizontal padding for text
  },
  title: {
    ...Typography.caption, // Changed from subtitle2 to caption for smaller text
    fontWeight: '600', // Add font weight for better readability
    color: Colors.onSurface,
    textAlign: 'center',
    marginBottom: 2, // Reduced margin
    lineHeight: 14, // Better line height for small text
  },
  subtitle: {
    ...Typography.caption,
    fontSize: 10, // Smaller font size for subtitle
    color: Colors.onSurfaceVariant,
    textAlign: 'center',
    lineHeight: 12,
  },
  disabled: {
    opacity: Animations.opacity.disabled,
  },
});

export default MenuCard;
