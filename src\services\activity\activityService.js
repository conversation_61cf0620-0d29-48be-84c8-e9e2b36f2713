/**
 * Activity Service
 * Handles activity data fetching, caching, and management for image galleries
 */

import { apiClient, ApiError } from '../api/apiClient';
import { PlaceholderImages } from '../../utils/placeholderImages';

class ActivityService {
  constructor() {
    this.cache = new Map();
    this.cacheExpiry = 5 * 60 * 1000; // 5 minutes
  }

  // Get featured activities
  async getFeaturedActivities(options = {}) {
    try {
      const { 
        limit = 10, 
        offset = 0, 
        category = null,
        forceRefresh = false 
      } = options;

      const cacheKey = `featured_activities_${limit}_${offset}_${category}`;
      
      // Check cache first
      if (!forceRefresh && this.isCacheValid(cacheKey)) {
        return this.cache.get(cacheKey).data;
      }

      // For development - return mock data
      if (__DEV__) {
        const mockData = await this.getMockFeaturedActivities(options);
        this.setCache(cacheKey, mockData);
        return mockData;
      }

      // Real API call
      const params = { limit, offset };
      if (category) params.category = category;

      const response = await apiClient.get('/activities/featured', params);
      const { data } = response;

      // Cache the result
      this.setCache(cacheKey, data);

      return data;

    } catch (error) {
      console.error('Error fetching featured activities:', error);
      throw new ApiError(
        error.message || 'Failed to fetch featured activities',
        error.status || 500,
        error.data || {}
      );
    }
  }

  // Get mock featured activities for development
  async getMockFeaturedActivities(options = {}) {
    const { limit = 10, offset = 0 } = options;
    
    // Simulate API delay
    await new Promise(resolve => setTimeout(resolve, 500));

    const mockActivities = [
      {
        id: 1,
        title: 'Team Building Event 2024',
        description: 'Kegiatan team building tahunan untuk mempererat kerjasama tim',
        image: PlaceholderImages.activities.teamBuilding,
        date: '2024-01-20T09:00:00Z',
        views: 1250,
        likes: 89,
        category: 'Events',
        tags: ['team-building', 'annual', 'collaboration'],
        location: 'Bogor, Indonesia',
        participants: 150,
        featured: true,
      },
      {
        id: 2,
        title: 'Safety Award Ceremony',
        description: 'Upacara penghargaan keselamatan kerja untuk karyawan terbaik',
        image: PlaceholderImages.activities.safetyAward,
        date: '2024-01-18T14:00:00Z',
        views: 890,
        likes: 67,
        category: 'Achievements',
        tags: ['safety', 'award', 'recognition'],
        location: 'Jakarta Office',
        participants: 80,
        featured: true,
      },
      {
        id: 3,
        title: 'New Office Opening',
        description: 'Peresmian kantor cabang baru PSG-BMI di Surabaya',
        image: PlaceholderImages.activities.officeOpening,
        date: '2024-01-15T10:30:00Z',
        views: 1450,
        likes: 123,
        category: 'Workplace',
        tags: ['office', 'opening', 'expansion'],
        location: 'Surabaya, Indonesia',
        participants: 200,
        featured: true,
      },
      {
        id: 4,
        title: 'Innovation Workshop',
        description: 'Workshop inovasi dan kreativitas untuk pengembangan produk',
        image: PlaceholderImages.activities.innovation,
        date: '2024-01-12T13:00:00Z',
        views: 678,
        likes: 45,
        category: 'Training',
        tags: ['innovation', 'workshop', 'creativity'],
        location: 'Training Center',
        participants: 60,
        featured: true,
      },
      {
        id: 5,
        title: 'Company Anniversary Celebration',
        description: 'Perayaan ulang tahun perusahaan ke-25 dengan berbagai acara menarik',
        image: PlaceholderImages.activities.anniversary,
        date: '2024-01-10T16:00:00Z',
        views: 2100,
        likes: 189,
        category: 'Events',
        tags: ['anniversary', 'celebration', 'milestone'],
        location: 'Head Office',
        participants: 300,
        featured: true,
      },
      {
        id: 6,
        title: 'Environmental Cleanup Drive',
        description: 'Kegiatan bersih-bersih lingkungan sebagai bagian dari CSR perusahaan',
        image: PlaceholderImages.activities.cleanup,
        date: '2024-01-08T07:00:00Z',
        views: 567,
        likes: 78,
        category: 'CSR',
        tags: ['environment', 'cleanup', 'csr'],
        location: 'Pantai Ancol',
        participants: 120,
        featured: true,
      },
      {
        id: 7,
        title: 'Digital Skills Training',
        description: 'Pelatihan keterampilan digital untuk meningkatkan produktivitas',
        image: PlaceholderImages.activities.training,
        date: '2024-01-05T09:30:00Z',
        views: 834,
        likes: 56,
        category: 'Training',
        tags: ['digital', 'skills', 'productivity'],
        location: 'Training Room A',
        participants: 40,
        featured: true,
      },
      {
        id: 8,
        title: 'Health & Wellness Fair',
        description: 'Pameran kesehatan dan kebugaran untuk karyawan dan keluarga',
        image: PlaceholderImages.activities.wellness,
        date: '2024-01-03T11:00:00Z',
        views: 1123,
        likes: 94,
        category: 'Wellness',
        tags: ['health', 'wellness', 'family'],
        location: 'Main Lobby',
        participants: 180,
        featured: true,
      },
    ];

    // Apply pagination
    const startIndex = offset;
    const endIndex = Math.min(startIndex + limit, mockActivities.length);
    const paginatedActivities = mockActivities.slice(startIndex, endIndex);

    return {
      activities: paginatedActivities,
      total: mockActivities.length,
      page: Math.floor(offset / limit) + 1,
      totalPages: Math.ceil(mockActivities.length / limit),
      hasMore: endIndex < mockActivities.length,
    };
  }

  // Record activity view
  async recordActivityView(activityId) {
    try {
      // For development - just log the view
      if (__DEV__) {
        console.log(`Activity view recorded: ${activityId}`);
        return { success: true };
      }

      // Real API call
      const response = await apiClient.post(`/activities/${activityId}/view`);
      return response.data;

    } catch (error) {
      console.error('Error recording activity view:', error);
      // Don't throw error for view tracking - it's not critical
      return { success: false };
    }
  }

  // Get activity by ID
  async getActivityById(activityId) {
    try {
      const cacheKey = `activity_${activityId}`;
      
      // Check cache first
      if (this.isCacheValid(cacheKey)) {
        return this.cache.get(cacheKey).data;
      }

      // For development - return mock data
      if (__DEV__) {
        const mockActivity = await this.getMockActivityById(activityId);
        this.setCache(cacheKey, mockActivity);
        return mockActivity;
      }

      // Real API call
      const response = await apiClient.get(`/activities/${activityId}`);
      const { data } = response;

      // Cache the result
      this.setCache(cacheKey, data);

      return data;

    } catch (error) {
      console.error('Error fetching activity:', error);
      throw new ApiError(
        error.message || 'Failed to fetch activity',
        error.status || 500,
        error.data || {}
      );
    }
  }

  // Get mock activity by ID for development
  async getMockActivityById(activityId) {
    await new Promise(resolve => setTimeout(resolve, 300));

    const featuredActivities = await this.getMockFeaturedActivities({ limit: 20 });
    const activity = featuredActivities.activities.find(a => a.id === parseInt(activityId));

    if (!activity) {
      throw new ApiError('Activity not found', 404);
    }

    return activity;
  }

  // Get activity categories
  async getActivityCategories() {
    try {
      const cacheKey = 'activity_categories';
      
      // Check cache first
      if (this.isCacheValid(cacheKey)) {
        return this.cache.get(cacheKey).data;
      }

      // For development - return mock data
      if (__DEV__) {
        const mockCategories = await this.getMockActivityCategories();
        this.setCache(cacheKey, mockCategories);
        return mockCategories;
      }

      // Real API call
      const response = await apiClient.get('/activities/categories');
      const { data } = response;

      // Cache the result
      this.setCache(cacheKey, data);

      return data;

    } catch (error) {
      console.error('Error fetching activity categories:', error);
      throw new ApiError(
        error.message || 'Failed to fetch activity categories',
        error.status || 500,
        error.data || {}
      );
    }
  }

  // Get mock activity categories for development
  async getMockActivityCategories() {
    await new Promise(resolve => setTimeout(resolve, 200));

    return {
      categories: [
        { id: 1, name: 'Events', count: 15, color: '#2196F3' },
        { id: 2, name: 'Training', count: 12, color: '#4CAF50' },
        { id: 3, name: 'Achievements', count: 8, color: '#FF9800' },
        { id: 4, name: 'Workplace', count: 10, color: '#9C27B0' },
        { id: 5, name: 'CSR', count: 6, color: '#00BCD4' },
        { id: 6, name: 'Wellness', count: 9, color: '#E91E63' },
      ]
    };
  }

  // Search activities
  async searchActivities(query, options = {}) {
    try {
      const { limit = 10, offset = 0, category = null } = options;
      const cacheKey = `search_activities_${query}_${limit}_${offset}_${category}`;

      // Check cache first
      if (this.isCacheValid(cacheKey)) {
        return this.cache.get(cacheKey).data;
      }

      // For development - return mock data
      if (__DEV__) {
        const mockResults = await this.getMockSearchResults(query, options);
        this.setCache(cacheKey, mockResults);
        return mockResults;
      }

      // Real API call
      const params = { q: query, limit, offset };
      if (category) params.category = category;

      const response = await apiClient.get('/activities/search', params);
      const { data } = response;

      // Cache the result
      this.setCache(cacheKey, data);

      return data;

    } catch (error) {
      console.error('Error searching activities:', error);
      throw new ApiError(
        error.message || 'Failed to search activities',
        error.status || 500,
        error.data || {}
      );
    }
  }

  // Get mock search results for development
  async getMockSearchResults(query, options = {}) {
    await new Promise(resolve => setTimeout(resolve, 400));

    const allActivities = await this.getMockFeaturedActivities({ limit: 20 });

    // Simple search implementation
    const filteredActivities = allActivities.activities.filter(activity =>
      activity.title.toLowerCase().includes(query.toLowerCase()) ||
      activity.description.toLowerCase().includes(query.toLowerCase()) ||
      activity.category.toLowerCase().includes(query.toLowerCase()) ||
      activity.tags.some(tag => tag.toLowerCase().includes(query.toLowerCase()))
    );

    const { limit = 10, offset = 0 } = options;
    const startIndex = offset;
    const endIndex = Math.min(startIndex + limit, filteredActivities.length);
    const paginatedResults = filteredActivities.slice(startIndex, endIndex);

    return {
      activities: paginatedResults,
      total: filteredActivities.length,
      query,
      page: Math.floor(offset / limit) + 1,
      totalPages: Math.ceil(filteredActivities.length / limit),
      hasMore: endIndex < filteredActivities.length,
    };
  }

  // Cache management methods
  isCacheValid(key) {
    const cached = this.cache.get(key);
    if (!cached) return false;

    const now = Date.now();
    return (now - cached.timestamp) < this.cacheExpiry;
  }

  setCache(key, data) {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
    });
  }

  clearCache() {
    this.cache.clear();
  }

  // Get cache stats
  getCacheStats() {
    const now = Date.now();
    let validEntries = 0;
    let expiredEntries = 0;

    for (const [key, value] of this.cache.entries()) {
      if ((now - value.timestamp) < this.cacheExpiry) {
        validEntries++;
      } else {
        expiredEntries++;
      }
    }

    return {
      totalEntries: this.cache.size,
      validEntries,
      expiredEntries,
      cacheExpiry: this.cacheExpiry,
    };
  }
}

// Create singleton instance
const activityService = new ActivityService();

export { activityService };
export default activityService;
